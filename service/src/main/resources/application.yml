server:
  port: 8580
spring:
  application:
    name: one-model-api  # 应用名称，方便日志、注册中心、监控识别
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  profiles:
    active: local
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 100MB
logging:
  level:
    com.onemodel: info  # 根据包名定义日志级别
    org.mybatis: warn
  file:
    name: logs/one-model-api.log
mybatis-plus:
  global-config:
    enable-sql-runner: true
    db-config:
      logic-delete-field: is_delete
      logic-delete-value: 1
      date-type: TIME_PACK
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath:/com/onemodel/mapper/xml/*.xml
  typeHandlersPackage: com.onemodel.mapper.config.handler











