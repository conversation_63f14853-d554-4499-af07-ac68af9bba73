CREATE TABLE patient_tb_empi
(
    empi_id                   BIGSERIAL PRIMARY KEY, -- HIS系统患者主索引ID
    pat_name                  VARCHAR(100) NOT NULL, -- 病人姓名，不允许为空
    certificate_type          VARCHAR(10)  NOT NULL, -- 证件类型，例如身份证、护照等，不允许为空
    certificate_no            VARCHAR(50)  NOT NULL, -- 证件号码，不允许为空
    birth_date                TIMESTAMP,             -- 病人出生日期，存储为时间戳类型，允许为空
    pat_sex                   INT,                   -- 病人性别
    nation                    INT,                   -- 病人的民族标识符，整数类型
    nationality               INT,                   -- 病人的国籍标识符，整数类型
    address                   VARCHAR(200),          -- 病人地址，最多200个字符
    card_no                   VARCHAR(50),           -- 病人的卡号（如医疗卡、社保卡等）
    region_code               VARCHAR(10),           -- 病人所在地区的编码
    card_type                 INT,                   -- 卡类型，整数类型，例如医保卡类型标识符
    telephone                 VARCHAR(20),           -- 病人联系电话，最多20个字符
    contacts_name             VARCHAR(20),           -- 紧急联系人姓名，最多20个字符
    contacts_relationship_phone VARCHAR(20),         -- 紧急联系人电话，最多20个字符
    status                    INT DEFAULT 1,         -- 状态
    created_by                INT,                   -- 创建记录的用户ID，整数类型
    created_date              TIMESTAMP,             -- 记录创建时间，存储为时间戳类型
    update_by                 INT,                   -- 最后更新记录的用户ID，整数类型
    update_date               TIMESTAMP,             -- 记录最后更新时间，存储为时间戳类型
    rule_code                 VARCHAR(20),           -- 规则编码，用于标识病人信息相关的规则
    qr_code_data             VARCHAR(50)            -- 存储病人二维码数据，例如用于快速访问病人信息
);


-- 为表中的每个字段添加描述
COMMENT
ON COLUMN patient_tb_empi.empi_id IS 'HIS系统患者主索引ID，自动递增的主键';
COMMENT
ON COLUMN patient_tb_empi.pat_name IS '病人姓名，不允许为空';
COMMENT
ON COLUMN patient_tb_empi.certificate_type IS '证件类型，例如身份证、护照等，不允许为空';
COMMENT
ON COLUMN patient_tb_empi.certificate_no IS '证件号码，不允许为空';
COMMENT
ON COLUMN patient_tb_empi.birth_date IS '病人出生日期，存储为时间戳类型，允许为空';
COMMENT
ON COLUMN patient_tb_empi.pat_sex IS '病人性别';
COMMENT
ON COLUMN patient_tb_empi.nation IS '病人的民族标识符，整数类型';
COMMENT
ON COLUMN patient_tb_empi.nationality IS '病人的国籍标识符，整数类型';
COMMENT
ON COLUMN patient_tb_empi.address IS '病人地址，最多200个字符';
COMMENT
ON COLUMN patient_tb_empi.card_no IS '病人的卡号（如医疗卡、社保卡等）';
COMMENT
ON COLUMN patient_tb_empi.region_code IS '病人所在地区的编码';
COMMENT
ON COLUMN patient_tb_empi.card_type IS '卡类型，整数类型，例如医保卡类型标识符';
COMMENT
ON COLUMN patient_tb_empi.telephone IS '病人联系电话，最多20个字符';
COMMENT
ON COLUMN patient_tb_empi.contacts_name IS '紧急联系人姓名，最多20个字符';
COMMENT
ON COLUMN patient_tb_empi.contacts_relationship_phone IS '紧急联系人电话，最多20个字符';
COMMENT
ON COLUMN patient_tb_empi.status IS '状态 默认为1';
COMMENT
ON COLUMN patient_tb_empi.created_by IS '创建记录的用户ID，整数类型';
COMMENT
ON COLUMN patient_tb_empi.created_date IS '记录创建时间，存储为时间戳类型';
COMMENT
ON COLUMN patient_tb_empi.update_by IS '最后更新记录的用户ID，整数类型';
COMMENT
ON COLUMN patient_tb_empi.update_date IS '记录最后更新时间，存储为时间戳类型';
COMMENT
ON COLUMN patient_tb_empi.rule_code IS '规则编码，用于标识病人信息相关的规则';
COMMENT
ON COLUMN patient_tb_empi.qr_code_data IS '存储病人二维码数据，例如用于快速访问病人信息';

-- 创建索引：根据证件号创建索引
CREATE INDEX idx_certificate_no ON patient_tb_empi (certificate_no);

-- 创建索引：根据二维码数据创建索引
CREATE INDEX ix_qr_code_data ON patient_tb_empi (qr_code_data);

CREATE TABLE patient_tb_empi_to_patient
(
    id           BIGSERIAL,                                 -- 自动递增的主键，PostgreSQL使用BIGSERIAL
    empi_id      BIGINT NOT NULL,                           -- HIS系统患者主索引ID，外联patient_tb_empi.empi_id
    identity_no  VARCHAR(50),                               -- HIS系统中的patId
    identity_type VARCHAR(20) DEFAULT 'HIS.PATINENTID',     -- 身份证类型，默认值为 'HIS.PATINENTID'
    status       INT         DEFAULT 1,                     -- 状态，默认为1
    created_by   INT,                                       -- 创建人ID，允许为空
    created_date TIMESTAMP,                                 -- 创建时间，允许为空
    update_by    INT,                                       -- 更新人ID，允许为空
    update_date  TIMESTAMP,                                 -- 更新时间，允许为空
    CONSTRAINT pk_patient_tb_empi_to_patient PRIMARY KEY (id) -- 主键约束
);

-- 创建 empi_id 索引
CREATE INDEX idx_empi_id ON patient_tb_empi_to_patient (empi_id);

-- 创建 identity_no 索引
CREATE INDEX idx_identity_no ON patient_tb_empi_to_patient (identity_no);

-- 创建 identity_type 和 status 索引
CREATE INDEX ix_identity_type ON patient_tb_empi_to_patient (identity_type, status, identity_no, empi_id);

-- 给表字段添加描述
COMMENT
ON COLUMN patient_tb_empi_to_patient.id IS '主键，自动递增的患者ID';
COMMENT
ON COLUMN patient_tb_empi_to_patient.empi_id IS '患者的EMPI ID';
COMMENT
ON COLUMN patient_tb_empi_to_patient.identity_no IS 'HIS系统中的patId';
COMMENT
ON COLUMN patient_tb_empi_to_patient.identity_type IS '身份证类型，默认值为''HIS.PATINENTID''';
COMMENT
ON COLUMN patient_tb_empi_to_patient.status IS '状态，默认为1';
COMMENT
ON COLUMN patient_tb_empi_to_patient.created_by IS '记录创建者的ID';
COMMENT
ON COLUMN patient_tb_empi_to_patient.created_date IS '记录创建时间';
COMMENT
ON COLUMN patient_tb_empi_to_patient.update_by IS '记录更新者的ID';
COMMENT
ON COLUMN patient_tb_empi_to_patient.update_date IS '记录更新时间';

