alter table diagnoses
    add add_diagnosis_code varchar(50);

-- column reordering is not supported diagnoses.add_diagnosis_code

comment on column diagnoses.add_diagnosis_code is '附加诊断编码';

alter table diagnoses
    add add_diagnosis_name text;

-- column reordering is not supported diagnoses.add_diagnosis_name

comment on column diagnoses.add_diagnosis_name is '附加诊断名称';

alter table diagnoses
    add is_check varchar(50);

-- column reordering is not supported diagnoses.is_check

comment on column diagnoses.is_check is '审核状态，记录该诊断是否已经审核';

alter table diagnoses
    add status integer;

-- column reordering is not supported diagnoses.status

comment on column diagnoses.status is '状态，诊断记录的状态（例如有效、无效）';

alter table diagnoses
    add is_delete integer default 0;

comment on column diagnoses.is_delete is '删除状态';

alter table diagnoses
    add delete_on timestamp;

comment on column diagnoses.delete_on is '删除时间，记录删除该诊断的时间';

alter table diagnoses
    add diagnosis_class text;

comment on column diagnoses.diagnosis_class is '诊断大类，标识诊断的主要类别（例如感染性疾病、肿瘤等）';

alter table diagnoses
    add diagnosis_grade integer;

comment on column diagnoses.diagnosis_grade is '诊断等级，表示诊断的严重程度或其他等级（可选）';

alter table diagnoses
    add property text;

comment on column diagnoses.property is '诊断属性，表示诊断的具体性质（例如遗传性、传染性等）';

alter table diagnoses
    add diag_kind_type text;

comment on column diagnoses.diag_kind_type is '精神科/躯体诊断，标明诊断是精神科还是躯体诊断';

alter table medical_orders
    add dosage_unit varchar(100);

comment on column medical_orders.dosage_unit is ' 剂量单位，剂量的单位（如毫克mg、毫升ml）';

alter table medical_orders
    add start_time timestamp;

comment on column medical_orders.start_time is '医嘱开始时间';

alter table medical_orders
    add stop_time timestamp;

comment on column medical_orders.stop_time is '医嘱停止时间';

alter table medical_orders
    add is_delete integer default 0;

comment on column medical_orders.is_delete is '可选值：0、未删除，1、删除';