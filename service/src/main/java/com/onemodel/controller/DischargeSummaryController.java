package com.onemodel.controller;

import com.onemodel.dto.DischargeSummaryRequestDTO;
import com.onemodel.service.DischargeSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.CrossOrigin;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;
import java.util.Objects;

/**
 * 出院小结和阶段小结接口控制器
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/dify-query")
public class DischargeSummaryController {

    @Resource
    private DischargeSummaryService dischargeSummaryService;

    /**
     * 根据住院号和医院代码获取病历信息，生成出院小结或阶段小结提示
     *
     * @param requestDTO 包含住院号、医院代码和应用类型的请求DTO
     * @return 出院小结或阶段小结的提示文本
     */
    @PostMapping("/discharge-summary")
    public ResponseEntity<?> getMedicalRecordsByRegno(@Valid @RequestBody DischargeSummaryRequestDTO requestDTO) {
        try {
            log.info("收到出院小结/阶段小结请求: {}", requestDTO);
            
            // 参数校验
            if (requestDTO.getRegno() == null || requestDTO.getHisHospitalCode() == null) {
                log.error("请求参数缺失: regno和his_hospital_code为必填项");
                return ResponseEntity.badRequest().body(Map.of("error", "Both regno and his_hospital_code are required"));
            }

            if (!(Objects.equals("出院小结", requestDTO.getAppType()) || Objects.equals("阶段小结", requestDTO.getAppType()))) {
                log.error("保存记录时，触发动作类型值不合法: {}, 只允许值为1或2", requestDTO.getAppType());
                return ResponseEntity.badRequest().body(Map.of("error", "Both app_type param data incorrect"));
            }

            // 调用服务层方法处理请求
            Map<String, Object> result = dischargeSummaryService.generateSummaryPromptV3(requestDTO);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("处理出院小结/阶段小结请求时发生错误", e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Exception: " + e.getMessage()));
        }
    }
} 