package com.onemodel.controller;

import com.onemodel.dto.MedicalOrdersDTO;
import com.onemodel.dto.MedicalRecordDTO;
import com.onemodel.service.SyncHisDataService;
import com.onemodel.util.JsonUtils;
import com.onemodel.util.ResultModel;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "his数据同步")
@CrossOrigin
@RestController
@RequestMapping("/hksoft_his")
public class SyncHisDataController {

    @Resource
    private SyncHisDataService syncHisDataService;

    /**
     * 医嘱同步
     */
    @PostMapping("/his_medical_order_sync")
    public ResultModel hisMedicalOrderSync(@RequestBody List<MedicalOrdersDTO> medicalOrders, HttpServletRequest httpServletRequest) {
        String hisHospitalCode = httpServletRequest.getHeader("Region-Id");
        log.info("接收医嘱同步请求，医院编码：{}，医嘱数据：{}", hisHospitalCode, JsonUtils.toJson(medicalOrders));

        try {
            Boolean b = syncHisDataService.hisMedicalOrderSync(medicalOrders, hisHospitalCode);
            Map<String, String> map = new HashMap<>();
            map.put("success", b ? "true" : "false");
            map.put("message", b ? "请求成功" : "保存数据失败");
            log.info("医嘱同步处理完成，结果：{}", b);
            return ResultModel.ok().data("content", map);
        } catch (Exception e) {
            log.error("医嘱同步处理异常", e);
            Map<String, String> map = new HashMap<>();
            map.put("success", "false");
            map.put("message", "处理异常：" + e.getMessage());
            return ResultModel.error().data("content", map);
        }
    }

    /**
     * 病历同步
     */
    @PostMapping("/his_medical_record_sync")
    public ResultModel hisMedicalRecordSync(@RequestBody List<MedicalRecordDTO> medicalRecordDTOS, HttpServletRequest httpServletRequest) {
        String hisHospitalCode = httpServletRequest.getHeader("Region-Id");
        log.info("接收病历同步请求，医院编码：{}，病历数据：{}", hisHospitalCode, JsonUtils.toJson(medicalRecordDTOS));

        try {
            Boolean b = syncHisDataService.hisMedicalRecordSync(medicalRecordDTOS, hisHospitalCode);
            Map<String, String> map = new HashMap<>();
            map.put("success", b ? "true" : "false");
            map.put("message", b ? "请求成功" : "保存数据失败");
            log.info("病历同步处理完成，结果：{}", b);
            return ResultModel.ok().data("content", map);
        } catch (Exception e) {
            log.error("病历同步处理异常", e);
            Map<String, String> map = new HashMap<>();
            map.put("success", "false");
            map.put("message", "处理异常：" + e.getMessage());
            return ResultModel.error().data("content", map);
        }
    }
}
