package com.onemodel.controller;

import com.onemodel.dto.EmrSynchronizationDTO;
import com.onemodel.service.EmrSynchronizationService;
import com.onemodel.util.ResultModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@Api(tags = "住院病历同步")
@CrossOrigin
@RestController
@RequestMapping("/hksoft_his")
public class EmrSynchronizationController {

    @Resource
    private EmrSynchronizationService emrSynchronizationService;

    @ApiOperation("住院病历同步")
    @PostMapping("/in_patient_emr_synchronization")
    public ResultModel synchronizeEmr(@RequestBody EmrSynchronizationDTO emrSynchronizationDTO) {
        try {
            log.info("接收到住院病历同步请求，病历ID: {}", emrSynchronizationDTO.getEmrId());

//            String result = emrSynchronizationService.processEmrSynchronization(emrSynchronizationDTO);

            Map<String, String> responseMap = new HashMap<>();
            responseMap.put("result", "success");

            log.info("住院病历同步处理完成，结果: {}", "success");
            return ResultModel.ok().data("content", responseMap);
        } catch (Exception e) {
            log.error("住院病历同步处理异常", e);

            Map<String, String> errorMap = new HashMap<>();
            errorMap.put("result", "error");
            errorMap.put("message", e.getMessage());

            return ResultModel.error().data("content", errorMap);
        }
    }
} 