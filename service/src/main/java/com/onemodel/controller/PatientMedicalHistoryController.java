package com.onemodel.controller;

import com.onemodel.dto.RegistrationNoticeDTO;
import com.onemodel.service.PatientMedicalHistoryService;
import com.onemodel.util.JsonUtils;
import com.onemodel.util.ResultModel;
import com.onemodel.vo.RegistrationNoticeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Api(tags = "挂号通知")
@CrossOrigin
@RestController
@RequestMapping("/hksoft_his")
public class PatientMedicalHistoryController {

    @Resource
    private PatientMedicalHistoryService patientMedicalHistoryService;


    @ApiOperation("挂号")
    @PostMapping("/B_PatientRegistration")
    public ResultModel registrationNotice(@RequestBody @Valid RegistrationNoticeDTO registrationNoticeDTO, HttpServletRequest httpServletRequest) throws IOException {

        String hisHospitalCode = httpServletRequest.getHeader("Region-Id");
        log.info("挂号完成接口-begin param1:{},param2:{},", hisHospitalCode, JsonUtils.toJson(registrationNoticeDTO));

        RegistrationNoticeVO registrationNoticeVO = patientMedicalHistoryService.registrationNoticeV3(registrationNoticeDTO, hisHospitalCode);

        if (!registrationNoticeVO.getPatient_exists()) {
            Map<String, String> map = new HashMap<>();
            map.put("success", "false");
            map.put("message", "查不到此患者");
            return ResultModel.ok().data("content", map);
        }
        if (!registrationNoticeVO.getMedical_history_exists()) {
            Map<String, String> map = new HashMap<>();
            map.put("success", "false");
            map.put("message", "查不到此患者就诊信息");
            return ResultModel.ok().data("content", map);
        }

        Map<String, String> map = new HashMap<>();
        map.put("success", "true");
        map.put("message", "请求成功");
        log.info("挂号完成接口-end:{}", JsonUtils.toJson(registrationNoticeVO));
        return ResultModel.ok().data("content", map);
    }

}
