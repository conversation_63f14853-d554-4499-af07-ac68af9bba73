package com.onemodel.service;

import com.onemodel.dto.DischargeSummaryRequestDTO;

import java.util.Map;

/**
 * 出院小结和阶段小结服务接口
 */
public interface DischargeSummaryService {
    
    /**
     * 根据住院号和医院代码生成出院小结或阶段小结的提示文本
     *
     * @param regno 住院号
     * @param hisHospitalCode 医院代码
     * @param appType 应用类型，如"出院小结"或"阶段小结"
     * @return 包含raw_prompt的结果Map
     * @throws Exception 处理过程中可能抛出的异常
     */
    Map<String, Object> generateSummaryPrompt(Integer regno, Integer hisHospitalCode, String appType) throws Exception;

    Map<String, Object> generateSummaryPromptV2(Integer regno, Integer hisHospitalCode, String appType) throws Exception;
    Map<String, Object> generateSummaryPromptV3(DischargeSummaryRequestDTO requestDTO) throws Exception;
}