package com.onemodel.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.onemodel.dto.HistoryDataQueryDTO;
import com.onemodel.entity.Patients;

import java.util.List;

/**
 * <p>
 * 患者基本信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface PatientsService extends IService<Patients> {
    @DS("sqlserver")
    Page<Patients> getHisPatientsInfo(HistoryDataQueryDTO historyDataQueryDTO);

    Patients getByHisId(Patients patients);

    List<Integer> getHisALLPatientId(Integer patientId);
}
