package com.onemodel.service;

import com.onemodel.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class DifySenderService {



    @Resource
    private RestTemplate outerRestTemplate;

    public void sendDify(Map<String, Object> records, String action, String regNo, String ai_url, String ai_key) {
        // Ensure none of the required values are null
        if (ObjectUtils.allNull(records, action, regNo)) {
            log.error("The records, action, and regNo are null.");
            return;
        }

        if (StringUtils.isBlank(ai_url) || StringUtils.isBlank(ai_key)) {
            log.error("The ai_url or ai_key is null.");
            return;
        }
        // Build the request body
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("response_mode", "streaming");
        requestBody.put("conversation_id", null);  // If needed, can be omitted
        requestBody.put("query", "既往病情回顾");
        requestBody.put("user", regNo);  // HIS传来的挂号流水号
        requestBody.put("files", Collections.emptyList());  // HIS传来的挂号流水号 (empty list)

        Map<String, String> inputs = new HashMap<>();
        inputs.put("action", action);

        // Convert records to JSON string and unescape it
        String recordsJson = JsonUtils.toJson(records.get("emr_record"));
        String unescapedJson = unescapeJson(recordsJson);
        inputs.put("records", unescapedJson);  // Assuming `unescapeJson` is implemented correctly

        requestBody.put("inputs", inputs);
        requestBody.put("parent_message_id", null);  // If needed, can be omitted

        // Set headers for the request
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);  // Set Content-Type as JSON
        headers.add("Authorization", "Bearer " + ai_key);

        // Create the request entity
        HttpEntity<String> requestEntity = new HttpEntity<>(JsonUtils.toJson(requestBody), headers);

        // Send the request using RestTemplate and handle the response
        try {
            ResponseEntity<String> response = outerRestTemplate.postForEntity(ai_url, requestEntity, String.class);
        } catch (Exception e) {
            log.error("Send Dify Error", e);
        }
    }

    public static String unescapeJson(String input) {
        // 处理转义字符：\\n -> \n, \\\" -> "
        String result = input;
        result = result.replaceAll("\\\\\\\"", "\"");  // 替换 \"
        return result;
    }
}

