package com.onemodel.service.impl;

import com.onemodel.annotation.SendDify;
import com.onemodel.dto.RegistrationNoticeDTO;
import com.onemodel.entity.MedicalRecordEntries;
import com.onemodel.entity.Patients;
import com.onemodel.entity.Visits;
import com.onemodel.service.MedicalRecordEntriesService;
import com.onemodel.service.PatientMedicalHistoryService;
import com.onemodel.service.PatientsService;
import com.onemodel.service.VisitsService;
import com.onemodel.vo.RegistrationNoticeEmrVO;
import com.onemodel.vo.RegistrationNoticeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PatientMedicalHistoryServiceImpl implements PatientMedicalHistoryService {


    @Resource
    private PatientsService patientsService;
    @Resource
    private VisitsService visitsService;
    @Resource
    private MedicalRecordEntriesService medicalRecordEntriesService;


    @SendDify
    @Override
    public RegistrationNoticeVO registrationNoticeV3(RegistrationNoticeDTO registrationNoticeDTO, String hisId) {
        RegistrationNoticeVO registrationNoticeVO = new RegistrationNoticeVO();
        // 检查hisId是否为空
        if (StringUtils.isBlank(hisId)) {
            log.info("registrationNoticeV2 hisId is null, hisId:{}, registrationNoticeDTO:{}", hisId, registrationNoticeDTO);
            registrationNoticeVO.setPatient_exists(false);
            registrationNoticeVO.setMedical_history_exists(false);
            return registrationNoticeVO;
        }

        Integer patientId = Integer.valueOf(registrationNoticeDTO.getHis_patid());

        // 查询患者信息

        Optional<Patients> patientsOptional = patientsService.lambdaQuery()
                .eq(Patients::getHisPatientId, patientId)
                .list().stream().findFirst();

        // 如果患者信息不存在
        if (patientsOptional.isEmpty()) {
            log.info("registrationNoticeV2 patientsOptional is null, hisId:{}, registrationNoticeDTO:{}", hisId, registrationNoticeDTO);
            registrationNoticeVO.setPatient_exists(false);
            registrationNoticeVO.setMedical_history_exists(false);
            return registrationNoticeVO;
        }
        //获取患者所有patId-根据patientId 查询对应的EMPIId 再根据EMPIId 查询所有patId
        List<Integer> hisEmpiId = patientsService.getHisALLPatientId(patientsOptional.get().getHisPatientId());

        List<Integer> patientIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hisEmpiId)) {
            patientIds.addAll(hisEmpiId);
        } else {
            patientIds.add(patientId);
        }
        log.info("registrationNoticeV2 HisALLPatientId:{}", patientIds);


        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        registrationNoticeVO.setPatient_exists(true);
        registrationNoticeVO.setMedical_history_exists(false);


        // 复诊逻辑
        if (Objects.equals(registrationNoticeDTO.getVisit_flag(), "1")) {
            // 初诊病历
            List<Visits> firstVisits = visitsService.lambdaQuery()
                    .in(Visits::getPatientId, patientIds)
                    .eq(Visits::getVisitCategory, 1)
                    .eq(Visits::getVisitType, "初诊")
                    .orderByAsc(Visits::getVisitDate)
                    .list();

            List<RegistrationNoticeEmrVO> noticeEmrVOS = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(firstVisits)) {
                //设置就诊信息
                registrationNoticeVO.setMedical_history_exists(true);
                List<Integer> hisVisitIds = firstVisits.stream().map(Visits::getHisVisitId).collect(Collectors.toList());
                Map<Integer, Visits> integerVisitsMap = firstVisits.stream()
                        .collect(Collectors.toMap(
                                Visits::getHisVisitId,
                                Function.identity(),
                                (existing, replacement) -> existing));  // 保留第一个
                // 获取初诊病历数据
                Map<Integer, String> medicalRecordEntriesMap = getMedicalRecordEntriesMap(hisVisitIds, patientIds, "MZ01");
                if (ObjectUtils.isNotEmpty(medicalRecordEntriesMap)) {
                    // 添加标志，确保只放一条数据
                    boolean added = false;
                    for (Map.Entry<Integer, String> medicalRecord : medicalRecordEntriesMap.entrySet()) {
                        if (StringUtils.isNotBlank(medicalRecord.getValue()) && !added) {
                            RegistrationNoticeEmrVO registrationNoticeEmrVO = new RegistrationNoticeEmrVO();
                            registrationNoticeEmrVO.setVISIT_TYPE("初诊");
                            Visits visits = integerVisitsMap.get(medicalRecord.getKey());
                            if (ObjectUtils.isNotEmpty(visits)) {
                                registrationNoticeEmrVO.setVISIT_DATE(sdf.format(visits.getVisitDate()));
                            }
                            registrationNoticeEmrVO.setVISIT_CONTENT(medicalRecord.getValue());
                            noticeEmrVOS.add(registrationNoticeEmrVO);
                            // 设置标志为已添加
                            added = true;
                        }
                    }
                }
            } else {
                return registrationNoticeVO;
            }
            // 复诊病历
            List<Visits> onceVisits = visitsService.lambdaQuery()
                    .in(Visits::getPatientId, patientIds)
                    .eq(Visits::getVisitCategory, 1)
                    .eq(Visits::getVisitType, "复诊")
                    .orderByAsc(Visits::getVisitDate)
                    .list();

            if (CollectionUtils.isNotEmpty(onceVisits)) {
                //设置就诊信息
                registrationNoticeVO.setMedical_history_exists(true);
                List<Integer> hisVisitIds = onceVisits.stream().map(Visits::getHisVisitId).collect(Collectors.toList());
                Map<Integer, Visits> integerVisitsMap = onceVisits.stream()
                        .collect(Collectors.toMap(
                                Visits::getHisVisitId,
                                Function.identity(),
                                (existing, replacement) -> existing));  // 保留第一个
                // 获取复诊病历
                Map<Integer, String> medicalRecordEntriesMap = getMedicalRecordEntriesMap(hisVisitIds, patientIds, "MZ02");
                if (ObjectUtils.isNotEmpty(medicalRecordEntriesMap)) {
                    for (Map.Entry<Integer, String> medicalRecord : medicalRecordEntriesMap.entrySet()) {
                        if (StringUtils.isNotBlank(medicalRecord.getValue())) {
                            RegistrationNoticeEmrVO registrationNoticeEmrVO = new RegistrationNoticeEmrVO();
                            registrationNoticeEmrVO.setVISIT_TYPE("复诊");
                            Visits visits = integerVisitsMap.get(medicalRecord.getKey());
                            if (ObjectUtils.isNotEmpty(visits)) {
                                registrationNoticeEmrVO.setVISIT_DATE(sdf.format(visits.getVisitDate()));
                            }
                            registrationNoticeEmrVO.setVISIT_CONTENT(medicalRecord.getValue());
                            noticeEmrVOS.add(registrationNoticeEmrVO);
                        }
                    }

                }
            }

            // 住院病历
            List<Visits> hospitalVisits = visitsService.lambdaQuery()
                    .in(Visits::getPatientId, patientIds)
                    .eq(Visits::getVisitCategory, 3)
                    .orderByAsc(Visits::getVisitDate)
                    .list();

            if (CollectionUtils.isNotEmpty(hospitalVisits)) {
                //设置就诊信息
                registrationNoticeVO.setMedical_history_exists(true);
                List<Integer> hisVisitIds = hospitalVisits.stream().map(Visits::getHisVisitId).collect(Collectors.toList());
                Map<Integer, Visits> integerVisitsMap = hospitalVisits.stream()
                        .collect(Collectors.toMap(
                                Visits::getHisVisitId,
                                Function.identity(),
                                (existing, replacement) -> existing));  // 保留第一个
                // 获取住院病历数据
                Map<Integer, String> medicalRecordEntriesMap = getMedicalRecordEntriesMap(hisVisitIds, patientIds, "BC4018");
                if (ObjectUtils.isNotEmpty(medicalRecordEntriesMap)) {
                    for (Map.Entry<Integer, String> medicalRecord : medicalRecordEntriesMap.entrySet()) {
                        if (StringUtils.isNotBlank(medicalRecord.getValue())) {
                            RegistrationNoticeEmrVO registrationNoticeEmrVO = new RegistrationNoticeEmrVO();
                            registrationNoticeEmrVO.setVISIT_TYPE("住院");
                            Visits visits = integerVisitsMap.get(medicalRecord.getKey());
                            if (ObjectUtils.isNotEmpty(visits)) {
                                registrationNoticeEmrVO.setVISIT_DATE(sdf.format(visits.getVisitDate()));
                            }
                            registrationNoticeEmrVO.setVISIT_CONTENT(medicalRecord.getValue());
                            noticeEmrVOS.add(registrationNoticeEmrVO);
                        }
                    }

                }
            }
            if (CollectionUtils.isNotEmpty(noticeEmrVOS)) {
                Map<String, Object> map = new HashMap<>();
                map.put("emr_record", noticeEmrVOS);
                registrationNoticeVO.setContent(map);
            }
        }
        return registrationNoticeVO;
    }


    /**
     * 根据就诊信息获取-病历信息
     *
     * @param visitIds       his系统-就诊id
     * @param patientId      his系统-患者id
     * @param recordCategory his系统-病历节点
     * @return
     */
    private Map<Integer, String> getMedicalRecordEntriesMap(List<Integer> visitIds, List<Integer> patientId, String recordCategory) {
        List<MedicalRecordEntries> medicalRecordEntries = medicalRecordEntriesService.lambdaQuery()
                .in(MedicalRecordEntries::getVisitId, visitIds)
                .in(MedicalRecordEntries::getPatientId, patientId)
                .eq(MedicalRecordEntries::getRecordType, "BodyText")
                .eq(StringUtils.isNotBlank(recordCategory), MedicalRecordEntries::getRecordCategory, recordCategory)
                .eq(MedicalRecordEntries::getStatus, '1')
                .eq(MedicalRecordEntries::getIsDelete, 0)
                .list();
        if (CollectionUtils.isNotEmpty(medicalRecordEntries)) {
            return medicalRecordEntries.stream()
                    .collect(Collectors.toMap(
                            MedicalRecordEntries::getVisitId,
                            MedicalRecordEntries::getTextContent,
                            (existing, replacement) -> existing));
        }
        return null;
    }


}
