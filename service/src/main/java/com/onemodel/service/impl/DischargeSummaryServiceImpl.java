package com.onemodel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.onemodel.dto.DischargeSummaryRequestDTO;
import com.onemodel.dto.MedicalRecordEntriesQueryDTO;
import com.onemodel.entity.InpatientMedicalRecord;
import com.onemodel.entity.MedicalOrders;
import com.onemodel.entity.MedicalRecordEntries;
import com.onemodel.mapper.MedicalRecordEntriesMapper;
import com.onemodel.mapper.VisitsMapper;
import com.onemodel.service.*;
import com.onemodel.util.FieldValueUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 出院小结和阶段小结服务实现类
 */
@Slf4j
@Service
public class DischargeSummaryServiceImpl implements DischargeSummaryService {

    @Resource
    private VisitsMapper visitsMapper;

    @Resource
    private MedicalRecordEntriesMapper medicalRecordEntriesMapper;


    @Resource
    private InpatientMedicalRecordService inpatientMedicalRecordService;


    @Resource
    private MedicalOrdersService medicalOrdersService;


    //单份病历
    private List<String> SINGLE_MEDICAL_RECORD = List.of("主任医师（疑难病例）讨论记录", "主任医师新病人（疑难病例）讨论记录",
            "主任医师新病人（非疑难）讨论记录",
            "入院记录",
            "主治医师新病人讨论记录",
            "首次病程录");
    //阶段小结
    private List<String>  STAGE_SUMMARY_FIRST= List.of("首次病程录");
    //多份病历
    private List<String> MULTIPLE_MEDICAL_RECORD = List.of("主治医师日常查房记录", "普通病程记录（含精神检查）", "一般病程录");
    //阶段小结-医嘱类型
    private List<String> STAGE_SUMMARY_ORDER_TYPE = List.of("治疗", "草药", "药品");
    //出院小结-医嘱类型
    private List<String> DISCHARGE_SUMMARY_ORDER_TYPE = List.of("出院带药", "治疗", "草药", "药品");
    //医嘱有效状态
    private List<String> MEDICAL_ORDER_VALIDITY_STATUS = List.of("1", "2", "3", "4");

    @Override
    public Map<String, Object> generateSummaryPrompt(Integer regno, Integer hisHospitalCode, String appType) throws Exception {
        log.info("开始生成出院小结/阶段小结提示，住院号: {}, 医院代码: {}, 应用类型: {}", regno, hisHospitalCode, appType);

        // 初始化结果模板
        Map<String, String> resultTemplate = new LinkedHashMap<>();
        resultTemplate.put("病程录", "");
        resultTemplate.put("首次病程录", "");
        resultTemplate.put("主诉", "");
        resultTemplate.put("现病史", "");
        resultTemplate.put("既往史", "");
        resultTemplate.put("精神检查小结", "");
        resultTemplate.put("躯体情况", "");
        resultTemplate.put("辅助检查", "");
        resultTemplate.put("药物治疗", "");
        resultTemplate.put("其他治疗", "");
        resultTemplate.put("主治医师日常查房记录", "");
        resultTemplate.put("门诊诊断", "");
        // 新增字段用于存储诊断和出院时间
        resultTemplate.put("入院诊断", "");
        resultTemplate.put("出院诊断", "");
        resultTemplate.put("出院时间", "");

        try {
            // 第一步：检查visits表中是否存在对应记录
            Integer visitsCount = visitsMapper.countByVisitIdAndHisHospitalCode(regno, hisHospitalCode);

            if (visitsCount == 0) {
                // 未找到就诊记录
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("error", "No visit record found with the provided regno and his_hospital_code");
                errorResult.put("data", resultTemplate);
                return errorResult;
            }

            // 第二步：查询医疗记录
            List<MedicalRecordEntries> medicalRecords = medicalRecordEntriesMapper.findByVisitIdAndHisHospitalCode(regno, hisHospitalCode);

            // 没有找到医疗记录时返回空模板
            if (medicalRecords.isEmpty()) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("message", "Visit found but no medical records available");
                emptyResult.put("data", resultTemplate);
                return emptyResult;
            }

            // 填充结果模板
            for (MedicalRecordEntries record : medicalRecords) {
                String category = record.getRecordType();
                String content = record.getTextContent() != null ? record.getTextContent() : "";

                // 如果类别在模板中存在，则添加内容
                if (resultTemplate.containsKey(category)) {
                    resultTemplate.put(category, content);
                }
            }

            // 查询诊断和出院时间信息
            try {
                // 查询诊断信息 - 这里需要根据实际的数据库结构实现
                // 例如，可以从diagnoses表中查询
                List<Map<String, Object>> diagnoses = medicalRecordEntriesMapper.findDiagnosesByVisitId(regno, hisHospitalCode);
                if (diagnoses != null && !diagnoses.isEmpty()) {
                    for (Map<String, Object> diagnosis : diagnoses) {
                        String type = (String) diagnosis.get("diagnosis_type");
                        String name = (String) diagnosis.get("diagnosis_name");

                        if ("入院诊断".equals(type) && name != null) {
                            resultTemplate.put("入院诊断", name);
                        } else if ("出院诊断".equals(type) && name != null) {
                            resultTemplate.put("出院诊断", name);
                        }
                    }
                }

                // 查询出院时间 - 从visits表中查询
                Map<String, Object> visitInfo = visitsMapper.findVisitInfoByHISId(regno, hisHospitalCode);
                if (visitInfo != null && visitInfo.get("discharge_time") != null) {
                    resultTemplate.put("出院时间", visitInfo.get("discharge_time").toString());
                }
            } catch (Exception e) {
                log.warn("查询诊断和出院时间信息时发生错误，将使用默认空值", e);
            }

            // 将模板数据转换为自然文本字符串
            StringBuilder rawText = new StringBuilder();
            for (Map.Entry<String, String> entry : resultTemplate.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                rawText.append(key)
                        .append(": ")
                        .append(value != null && !value.trim().isEmpty() ? value.trim() : "无数据")
                        .append(";\n");
            }

            // 在文本最后添加请求的句子（用于分支判断出院还是阶段）
            rawText.append("\n\n【根据上述患者信息生成:").append(appType).append("】");

            // 返回包含原始文本的结果
            Map<String, Object> result = new HashMap<>();
            result.put("raw_prompt", rawText.toString());
            return result;

        } catch (Exception e) {
            log.error("生成出院小结/阶段小结提示时发生错误", e);
            throw e;
        }
    }

    @Override
    public Map<String, Object> generateSummaryPromptV2(Integer regno, Integer hisHospitalCode, String appType) {

        log.info("开始生成出院小结/阶段小结提示，住院号: {}, 医院代码: {}, 应用类型: {}", regno, hisHospitalCode, appType);
        try {


            List<InpatientMedicalRecord> medicalRecords = inpatientMedicalRecordService.lambdaQuery()
                    .eq(InpatientMedicalRecord::getVisitId, regno)
                    .eq(InpatientMedicalRecord::getHisHospitalCode, hisHospitalCode)
                    .eq(InpatientMedicalRecord::getActionType, Objects.equals("出院小结", appType) ? 1 : 2)
                    .list();

            StringBuilder rawText = new StringBuilder();

            if (CollectionUtils.isNotEmpty(medicalRecords)) {
                Map<String, List<InpatientMedicalRecord>> inpatientMedicalRecordMap = medicalRecords.stream().collect(Collectors.groupingBy(InpatientMedicalRecord::getRecordCategory));
                for (Map.Entry<String, List<InpatientMedicalRecord>> entry : inpatientMedicalRecordMap.entrySet()) {
                    String key = entry.getKey();
                    if (Objects.equals("阶段小结", key)) {
                        continue;
                    }
                    // 判断是否是“主任医师（疑难病例）讨论记录”
                    boolean isDirectorDiscussion = Objects.equals("主任医师（疑难病例）讨论记录", key);
                    // 处理记录内容
                    StringBuilder stringBuilder = new StringBuilder();
                    entry.getValue().forEach(record -> {
                        String recordContent = "【" + record.getRecordType() + "：" + record.getTextContent() + "】";
                        // 如果是主任医师（疑难病例）讨论记录去除精神检查内容
                        if (isDirectorDiscussion) {
                            recordContent = recordContent
                                    .replaceAll("【精神检查：[^】]*】", "")
                                    .replaceAll("【精神检查:[^】]*】", "");
                        }
                        stringBuilder.append(recordContent).append("\n");
                    });

                    rawText.append(entry.getKey()).append(":").append(stringBuilder).append("\n");
                }

                if (Objects.equals("阶段小结", appType)) {
                    List<InpatientMedicalRecord> records = inpatientMedicalRecordMap.get("阶段小结");
                    if (CollectionUtils.isNotEmpty(records)) {
                        rawText.append("阶段小结").append(":");
                        records.forEach(x -> {
                            if (Objects.equals("记录日期", x.getRecordType()))
                                rawText.append("【记录日期:").append(x.getTextContent()).append("】");
                        });
                    }
                }
            }
            // 在文本最后添加请求的句子（用于分支判断出院还是阶段）
            rawText.append("\n\n【根据上述患者信息生成:").append(appType).append("】");


            // 返回包含原始文本的结果
            Map<String, Object> result = new HashMap<>();
            result.put("raw_prompt", rawText.toString());
            return result;
        } catch (Exception e) {
            log.error("生成出院小结/阶段小结提示时发生错误", e);
            throw e;
        }
    }

    @Override
    public Map<String, Object> generateSummaryPromptV3(DischargeSummaryRequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        if (Objects.equals("阶段小结", requestDTO.getAppType())) {
            stringBuilder = getStageSummary(requestDTO);

        } else {
            stringBuilder = getDischargeSummary(requestDTO);
            // 在文本最后添加请求的句子（用于分支判断出院还是阶段）

        }
        if (StringUtils.isNotBlank(stringBuilder.toString())) {
            stringBuilder.append("\n\n【根据上述患者信息生成:").append(requestDTO.getAppType()).append("】");

            // 返回包含原始文本的结果
            result.put("raw_prompt", stringBuilder.toString());
            return result;
        }

        // 返回包含原始文本的结果
        result.put("raw_prompt", "");
        return result;
    }

    /**
     * 出院小结
     *
     * @param requestDTO 请求参数
     * @return 出院小结内容
     * @throws Exception 日期解析异常
     */
    private StringBuilder getDischargeSummary(DischargeSummaryRequestDTO requestDTO) throws Exception {
        // 1. 计算获取病历的时间范围
        Date[] timeRange = calculateTimeRange(requestDTO.getSummaryTime(), 7, Calendar.DAY_OF_WEEK);
        Date beginTime = timeRange[0];
        Date endTime = timeRange[1];

        // 2. 构建结果文本
        StringBuilder rawText = new StringBuilder(1024);

        // 3. 获取病历内容（一周内）
        appendMedicalRecords(rawText, requestDTO, beginTime, endTime, MULTIPLE_MEDICAL_RECORD);

        // 4. 获取单份病历（不限时间）
        appendMedicalRecords(rawText, requestDTO, null, null, SINGLE_MEDICAL_RECORD);

        // 5. 获取医嘱记录（一周内）
        appendMedicalOrders(rawText, requestDTO, beginTime, endTime, DISCHARGE_SUMMARY_ORDER_TYPE);

        return rawText;
    }

    /**
     * 阶段小结
     *
     * @param requestDTO 请求参数
     * @return 阶段小结内容
     * @throws ParseException 日期解析异常
     */
    private StringBuilder getStageSummary(DischargeSummaryRequestDTO requestDTO) throws ParseException {
        // 1. 设置结束时间
        Date[] timeRange = calculateTimeRange(requestDTO.getSummaryTime(), 1, Calendar.MONTH);
        Date endTime = timeRange[1];
        Date beginTime = timeRange[0];
        ;

        // 2. 构建结果文本
        StringBuilder rawText = new StringBuilder(1024);

        // 3. 查询是否有阶段小结记录
        MedicalRecordEntriesQueryDTO queryDTO = buildQueryDTO(requestDTO, "阶段小结");
        List<MedicalRecordEntries> medicalRecordEntries = medicalRecordEntriesMapper.findEmr(queryDTO);

        // 4. 处理非首次小结
        if (CollectionUtils.isNotEmpty(medicalRecordEntries)) {
            // 添加上一次阶段小结
            String fieldValues = FieldValueUtils.getFieldValues(medicalRecordEntries.get(0));
            rawText.append("【上一次阶段小结:").append(fieldValues).append("】");

            // 获取病历信息（一个月内）
            appendMedicalRecords(rawText, requestDTO, beginTime, endTime, MULTIPLE_MEDICAL_RECORD);
            // 获取单份病历（不限时间）
            appendMedicalRecords(rawText, requestDTO, null, null, STAGE_SUMMARY_FIRST);
        } else {
            // 5. 处理首次阶段小结
            beginTime = getFirstRecordDate(requestDTO);
            if (beginTime != null) {
                // 获取病历内容（从入院到当前）
                appendMedicalRecords(rawText, requestDTO, beginTime, endTime, MULTIPLE_MEDICAL_RECORD);

                // 获取单份病历（不限时间）
                appendMedicalRecords(rawText, requestDTO, null, null, SINGLE_MEDICAL_RECORD);
            }
        }

        // 6. 添加医嘱记录
        appendMedicalOrders(rawText, requestDTO, beginTime, endTime, STAGE_SUMMARY_ORDER_TYPE);

        return rawText;
    }

    /**
     * 计算时间范围
     *
     * @param summaryTimeStr 小结时间字符串
     * @param daysToSubtract 需要减去的天数，0表示不减
     * @return 时间范围数组[beginTime, endTime]
     * @throws ParseException 日期解析异常
     */
    private Date[] calculateTimeRange(String summaryTimeStr, int daysToSubtract, int field) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date endTime = sdf.parse(summaryTimeStr);

        // 设置endTime为当天的23:59:59
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endTime);
        endCalendar.set(Calendar.HOUR_OF_DAY, 23);
        endCalendar.set(Calendar.MINUTE, 59);
        endCalendar.set(Calendar.SECOND, 59);
        endTime = endCalendar.getTime();

        // 计算开始时间
        Date beginTime = null;
        if (daysToSubtract > 0) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endTime);
            calendar.add(field, -daysToSubtract);
            beginTime = calendar.getTime();
        }

        return new Date[]{beginTime, endTime};
    }

    /**
     * 获取首次病程录记录时间
     *
     * @param requestDTO 请求参数
     * @return 首次病程录记录时间，如果没有则返回null
     */
    private Date getFirstRecordDate(DischargeSummaryRequestDTO requestDTO) {
        MedicalRecordEntriesQueryDTO queryDTO = buildQueryDTO(requestDTO, "首次病程录");
        List<MedicalRecordEntries> firstDiseaseRecordEmr = medicalRecordEntriesMapper.findEmr(queryDTO);

        if (!CollectionUtils.isEmpty(firstDiseaseRecordEmr)) {
            MedicalRecordEntries firstDiseaseEmr = firstDiseaseRecordEmr.get(0);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(firstDiseaseEmr.getRecordDate());
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            return calendar.getTime();
        }

        return null;
    }

    /**
     * 构建查询DTO
     *
     * @param requestDTO 请求参数
     * @param category   类别
     * @return 查询DTO
     */
    private MedicalRecordEntriesQueryDTO buildQueryDTO(DischargeSummaryRequestDTO requestDTO, String category) {
        MedicalRecordEntriesQueryDTO queryDTO = new MedicalRecordEntriesQueryDTO();
        queryDTO.setVisitId(requestDTO.getRegno());
        queryDTO.setHisHospitalCode(requestDTO.getHisHospitalCode());
        queryDTO.setCategoryList(Collections.singletonList(category));
        return queryDTO;
    }

    /**
     * 添加医疗记录到结果中
     *
     * @param rawText      结果文本
     * @param requestDTO   请求参数
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @param categoryList 类别列表
     */
    private void appendMedicalRecords(StringBuilder rawText, DischargeSummaryRequestDTO requestDTO,
                                      Date beginTime, Date endTime, List<String> categoryList) {
        Map<String, List<String>> emrMap = getEmrByMonth(requestDTO, beginTime, endTime, categoryList);
        if (!ObjectUtils.isEmpty(emrMap)) {
            for (Map.Entry<String, List<String>> entry : emrMap.entrySet()) {
                rawText.append("【").append(entry.getKey()).append(":").append("\n\n");
                for (String content : entry.getValue()) {
                    rawText.append(content);
                }
                rawText.append("】").append("\n\n");
            }
        }
    }

    /**
     * 添加医嘱记录到结果中
     *
     * @param rawText    结果文本
     * @param requestDTO 请求参数
     * @param beginTime  开始时间
     * @param endTime    结束时间
     * @param orderTypes 医嘱类型列表
     * @throws ParseException 日期解析异常
     */
    private void appendMedicalOrders(StringBuilder rawText, DischargeSummaryRequestDTO requestDTO,
                                     Date beginTime, Date endTime, List<String> orderTypes) throws ParseException {
        Map<String, List<String>> medicalOrderMap = getMedicalOrders(requestDTO, beginTime, endTime, orderTypes);
        if (!ObjectUtils.isEmpty(medicalOrderMap)) {
            for (Map.Entry<String, List<String>> entry : medicalOrderMap.entrySet()) {
                rawText.append("【医嘱记录-").append(entry.getKey()).append(":").append("\n\n");
                for (String content : entry.getValue()) {
                    rawText.append(content).append("\n");
                }
                rawText.append("】").append("\n\n");
            }
        }
    }


    private Map<String, List<String>> getEmrByMonth(DischargeSummaryRequestDTO requestDTO, Date beginTime, Date endTime, List<String> categoryCode) {

        MedicalRecordEntriesQueryDTO queryDTO = new MedicalRecordEntriesQueryDTO();
        queryDTO.setVisitId(requestDTO.getRegno());
        queryDTO.setHisHospitalCode(requestDTO.getHisHospitalCode());
        if (ObjectUtils.isNotEmpty(endTime)) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endTime);
            // 减去一个月
            calendar.add(Calendar.MONTH, -1);
            if (ObjectUtils.isEmpty(beginTime)) {
                beginTime = calendar.getTime();
            }
        }

        queryDTO.setBeginTime(beginTime);
        queryDTO.setEndTime(endTime);
        queryDTO.setCategoryList(categoryCode);
        List<MedicalRecordEntries> entriesMapperEmr = medicalRecordEntriesMapper.findEmr(queryDTO);
        if (CollectionUtils.isNotEmpty(entriesMapperEmr)) {
            Map<String, List<MedicalRecordEntries>> stringListMap = entriesMapperEmr.stream().collect(Collectors.groupingBy(MedicalRecordEntries::getCategoryName));
            Map<String, List<String>> emrMap = new HashMap<>();
            for (Map.Entry<String, List<MedicalRecordEntries>> entry : stringListMap.entrySet()) {
                List<String> list = entry.getValue().stream().map(FieldValueUtils::getFieldValues).toList();
                emrMap.put(entry.getKey(), list);
            }
            return emrMap;
        }
        return new HashMap<>();
    }


    /**
     * 查询医嘱：用药、治疗
     *
     * @param requestDTO
     * @return
     */
    private Map<String, List<String>> getMedicalOrders(DischargeSummaryRequestDTO requestDTO, Date beginTime, Date endTime, List<String> orderTypes) throws ParseException {

        LambdaQueryWrapper<MedicalOrders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MedicalOrders::getVisitId, requestDTO.getRegno())
                .eq(MedicalOrders::getHisHospitalCode, requestDTO.getHisHospitalCode())
                .eq(MedicalOrders::getOrgType,"ZY")
                .in(MedicalOrders::getOrderType, orderTypes)
                .in(MedicalOrders::getStatus, MEDICAL_ORDER_VALIDITY_STATUS)
                .and(wrapper -> wrapper.between(MedicalOrders::getStartTime, beginTime, endTime)
                        .or().between(MedicalOrders::getStopTime, beginTime, endTime)
                        .or(wrapper1 -> wrapper1.le(MedicalOrders::getStartTime, beginTime)
                                .ge(MedicalOrders::getStopTime, endTime)));
        queryWrapper.orderByDesc(MedicalOrders::getStartTime);


        List<MedicalOrders> medicalOrders = medicalOrdersService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(medicalOrders)) {

            Map<String, List<MedicalOrders>> stringListMap = medicalOrders.stream().map(x -> x.setStatus(getOrderStatusName(x.getStatus()))).collect(Collectors.groupingBy(MedicalOrders::getOrderType));
            Map<String, List<String>> map = new HashMap<>();
            for (Map.Entry<String, List<MedicalOrders>> entry : stringListMap.entrySet()) {
                map.put(entry.getKey(), entry.getValue().stream().map(FieldValueUtils::getFieldValues).toList());

            }
            return map;
        }
        return new HashMap<>();
    }

    private String getOrderStatusName(String status) {

        return switch (status) {
            case "-1" -> "新增";
            case "0" -> "待审";
            case "1" -> "有效";
            case "2" -> "待停";
            case "3" -> "已停";
            case "4" -> "暂停";
            case "5" -> "重整";
            case "9" -> "作废";
            default -> "未知";
        };
    }
} 