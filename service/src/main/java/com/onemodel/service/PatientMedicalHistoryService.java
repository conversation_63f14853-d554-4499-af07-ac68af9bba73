package com.onemodel.service;

import com.onemodel.dto.RegistrationNoticeDTO;
import com.onemodel.vo.RegistrationNoticeVO;

import java.io.IOException;


public interface PatientMedicalHistoryService {


    /**
     * 挂号通知
     * @param registrationNoticeDTO
     * @return
     */
   RegistrationNoticeVO registrationNoticeV3(RegistrationNoticeDTO registrationNoticeDTO,String hisHospitalCode) throws IOException;
}
