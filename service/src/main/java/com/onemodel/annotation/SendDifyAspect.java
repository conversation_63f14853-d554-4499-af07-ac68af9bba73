package com.onemodel.annotation;

import com.onemodel.dto.RegistrationNoticeDTO;
import com.onemodel.service.DifySenderService;
import com.onemodel.util.JsonUtils;
import com.onemodel.vo.RegistrationNoticeVO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Aspect
@Component  // 使切面成为 Spring 管理的 Bean
public class SendDifyAspect {

    @Resource
    private DifySenderService difySender;  // 注入发送消息的组件（DifySender）

    @Pointcut("@annotation(SendDify)")  // 匹配标注了 @SendDify 的方法
    public void sendDifyPointcut() {
    }

    @Async
    @AfterReturning(pointcut = "sendDifyPointcut() && args(registrationNoticeDTO,..)", returning = "result", argNames = "registrationNoticeDTO,result")
    public void sendMessageAfterMethod(RegistrationNoticeDTO registrationNoticeDTO, RegistrationNoticeVO result) throws Throwable {
        log.info("SendDifyAspect.sendMessageAfterMethod begin");

        log.info("RegistrationNoticeDTO:{},RegistrationNoticeVO:{}", JsonUtils.toJson(registrationNoticeDTO), JsonUtils.toJson(result));
        // 获取 RegistrationNoticeDTO 中的参数，假设它是传递到方法中的第一个参数
        if (registrationNoticeDTO != null) {
            // 获取 action 和 regNo，假设 registrationNoticeDTO 中包含这些字段
            String action = registrationNoticeDTO.getAction();  // 获取 action
            String regNo = registrationNoticeDTO.getRegno();    // 获取 regNo
            //患者信息+就诊信息都存在的情况
            if (result.getMedical_history_exists() && result.getPatient_exists()) {
                // 如果 result.getContent() 不是空的，则发送 Dify 消息
                if (result != null && result.getContent() != null) {
                    // 调用 DifySender 的 sendDify 方法发送消息
                    difySender.sendDify(result.getContent(), action, regNo, registrationNoticeDTO.getAi_url(), registrationNoticeDTO.getAi_key());
                } else {
                    // 处理返回内容为空的情况
                    log.error("The content of the result is null.");
                }
            }
        } else {
            // 处理 RegistrationNoticeDTO 为空的情况
            log.error("The RegistrationNoticeDTO is null.");
        }
        log.info("SendDifyAspect.sendMessageAfterMethod end");

    }
}

