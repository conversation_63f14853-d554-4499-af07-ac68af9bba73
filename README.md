# 项目名称

## 简介

精总AI助理后端服务

## 技术栈

- Spring Boot版本：2.7.0
- 数据库：PostgreSQL、SQL Server
- 其他技术：Lombok, Swagger, MyBatis-Plus

## 安装与运行

### 前提条件

列出运行项目所需的环境或工具，例如：

- Java版本：OpenJDK 17
- Maven：3.6+
- 数据库：PostgreSQL 12+，SQL Server 2017+

### 安装步骤

1. 克隆项目仓库到本地：https://git.taihealth.cn/one-model/model-service-his-api.git
2. 打开项目在IDE中。
3. 使用Maven项目。
4. 配置数据库连接（在`application.yml`中设置）。
5. 运行Spring Boot应用。

### 配置说明

项目使用以下配置文件:

- `application.yml`: 主配置文件，包含通用配置
- `application-local.yml`: 本地环境配置
- `application-test.yml`: 测试环境配置

主要配置项包括:

- 服务端口: 8580
- 数据库连接: PostgreSQL(主数据库)和SQL Server(HIS数据库)
- 精总HIS服务接口


### medical-history-review 配置说明
- medical-history-review:
- first: 门诊-初诊病历
- record-type: 病历数据记录类型（his_ai_db.record_types（表）.type_code）
- record-category: 病历分类（取自his_ai_db.record_categories（表）.category_code）
- once: 门诊-复诊病历
- record-type: 病历数据记录类型（his_ai_db.record_types（表）.type_code）
- record-category: 病历分类（取自his_ai_db.record_categories（表）.category_code）
- hospital: 住院病历-出院小结
- record-type: 病历数据记录类型（his_ai_db.record_types（表）.type_code）
- record-category: 病历分类（取自his_ai_db.record_categories（表）.category_code）

### 目录说明

目录文件夹用途说明

- [controller](service/src/main/java/com/onemodel/controller)：控制器层（API接口）
- [config](service/src/main/java/com/onemodel/config)：配置层
- [entity](service/src/main/java/com/onemodel/entity)：DB 实体类层
- [mapper](service/src/main/java/com/onemodel/mapper)：MyBatis mapper层
- [service](service/src/main/java/com/onemodel/service)：service层：逻辑处理
- [task](service/src/main/java/com/onemodel/task)：定时任务层
- [util](service/src/main/java/com/onemodel/util)：工具类
- [vo](service/src/main/java/com/onemodel/vo)：外部返回实体类VO层
- [dto](service/src/main/java/com/onemodel/dto)：内部传输实体类DTO层
- [annotation](service/src/main/java/com/onemodel/annotation)：自定义注解
- [resources](service/src/main/resources)：静态资源
- [test](service/src/test)：测试层
- [his_sql](service/src/main/resources/his_sql)：精总HIS提供抽取数据sql脚本

### HIS SQL脚本说明

`resources/his_sql`目录下包含从精总HIS系统抽取数据的SQL脚本:

- `patients.sql`: 患者基本信息抽取脚本
- `门诊就诊记录.sql`: 门诊就诊记录抽取脚本
- `住院就诊记录.sql`: 住院就诊记录抽取脚本
- `departments.sql`: 科室信息抽取脚本
- `门诊诊断.sql`: 门诊诊断信息抽取脚本
- `住院诊断.sql`: 住院诊断信息抽取脚本
- `doctors.sql`: 医生基本信息抽取脚本
- `门诊医嘱.sql`: 门诊医嘱抽取脚本
- `住院医嘱.sql`: 住院医嘱抽取脚本
- `record_types.sql`: 病历类型抽取脚本
- `record_categories.sql`: 病历分类抽取脚本

`resources/ai_db_sql`目录下包含从onemodel服务所需数据库脚本:

- `his_ai_db_ddl_ddl.sql`: 数据库表创建脚本
- `his_ai_db_01_ddl_002.sql`: 数据库表创建脚本(更新过表描述的版本)
- `inpatient_medical_record_001_ddl.sql`: 住院病历数据记录表创建脚本
- `patient_tb_empi_001_ddl.sql`: 对应HIS系统中的患者EMPI对应关系表

这些SQL脚本用于从HIS系统中提取数据，并转换为标准化格式，供AI助理使用。

### 定时任务机制

系统通过定时任务自动从HIS系统提取数据。定时任务实现位于`com.onemodel.task.HistoryDataLoadTaskJob`类。

主要特点:

- 基于Spring的`@Scheduled`注解实现
- 所有任务默认每2分钟执行一次 (cron = "0 0/2 * * * ?")
- 任务执行参数通过数据库配置表控制，配置代码为"HIS_DB_LOAD"

包含的定时任务:

- 科室数据同步 (loadDepartments)
- 患者信息同步 (loadPatientsInfo)
- 门诊-就诊记录同步 (loadVisits)
- 住院-就诊记录同步 (loadVisitsZ)
- 病历数据同步 (loadMedicalRecordEntries)
- 门诊-医嘱数据同步 (loadMedicalOrders)
- 住院-医嘱数据同步 (loadMedicalOrdersZ)
- 医生信息同步 (loadDoctors)
- 门诊-诊断信息同步 (loadDiagnoses)
- 住院-诊断信息同步 (loadDiagnosesZ)
- 病历分类同步 (loadRecordCategories)
- 病历类型同步 (loadRecordTypes)
- 辅助检查同步 (loadAuxiliaryExaminations)

配置项说明:

- SYNC_Status: 控制同步开关，值为"true"时启用同步
- SYNC_Size: 每次同步的数据批次大小


## 使用指南

### 开发配置
本地开发，先拷贝`application.yml.example`文件为`application-local.yml`，然后修改配置

```
cp application.yml.example application-local.yml
```

### 环境配置

根据不同环境，修改`application.yml`中的`spring.profiles.active`值:

- 本地环境: `local`
- 测试环境: `test`

### 测试部署

1. 打jar包：mvn package

### Docker部署

项目支持使用Docker容器化部署，步骤如下：

1. 打包应用：
   ```
   mvn clean package -DskipTests
   ```

2. 构建Docker镜像：
   ```
   docker build --platform linux/amd64 -t registry.cn-shanghai.aliyuncs.com/chos/one-model-service-api:1.0 .
   ```

3. 环境变量配置（可选）：
   - 本地环境：
   ```
   docker run -d -p 8580:8580 \
     -e SPRING_PROFILES_ACTIVE=local \
     -v $(pwd)/application-local.yml:/config/application.yml \
     --name one-model-api \
     registry.cn-shanghai.aliyuncs.com/chos/one-model-service-api:1.0
   ```
   - 测试环境：
   ```
   docker run -d -p 8580:8580 \
     -e SPRING_PROFILES_ACTIVE=test \
     -v $(pwd)/application-test.yml:/config/application.yml \
     --name one-model-api \
     registry.cn-shanghai.aliyuncs.com/chos/one-model-service-api:1.0
   ```
   - 生产环境：
   ```
   docker run -d -p 8580:8580 \
     -e SPRING_PROFILES_ACTIVE=prod \
     -v $(pwd)/application-prod.yml:/config/application.yml \
     --name one-model-api \
     registry.cn-shanghai.aliyuncs.com/chos/one-model-service-api:1.0
   ```

4. 查看容器日志：
   ```
   docker logs -f one-model-api
   ```

### 系统功能

本系统主要提供以下功能:

1. 对接精总HIS系统，抽取临床数据
2. 为AI助理提供结构化医疗数据
3. 提供RESTful API接口供上层应用调用
